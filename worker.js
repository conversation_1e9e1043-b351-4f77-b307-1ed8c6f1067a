addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

const SONG_URL_API = 'https://88.lxmusic.xn--fiqs8s/lxmusicv3/url/wy';

function withCors(response) {
  const newHeaders = new Headers(response.headers)
  newHeaders.set('Access-Control-Allow-Origin', '*')
  newHeaders.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  newHeaders.set('Access-Control-Allow-Headers', 'Content-Type')
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  })
}

async function handleRequest(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    })
  }

  const url = new URL(request.url)

  if (url.pathname === '/api/search') {
    const keyword = url.searchParams.get('keywords') || url.searchParams.get('s') || ''
    const limit = url.searchParams.get('limit') || '10'
    const offset = url.searchParams.get('offset') || '0'

    if (!keyword) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing keywords' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }))
    }

    const targetUrl = `https://netease-cloud-music-api.vercel.app/search?keywords=${encodeURIComponent(keyword)}&limit=${limit}&offset=${offset}`

    try {
      const resp = await fetch(targetUrl)
      const contentType = resp.headers.get('content-type') || 'application/json'
      const body = await resp.text()
      return withCors(new Response(body, {
        status: resp.status,
        headers: { 'content-type': contentType },
      }))
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }))
    }
  }

  if (url.pathname === '/api/get-song-url') {
    const songId = url.searchParams.get('songId');
    const quality = url.searchParams.get('quality') || 'flac';
    if (!songId) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing songId' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
    const targetUrl = `${SONG_URL_API}/${songId}/${quality}`;
    try {
      const resp = await fetch(targetUrl);
      const data = await resp.json();
      if (data.data) {
        const songUrl = data.data.replace(/\\/g, '');
        return withCors(new Response(JSON.stringify({ code: 200, data: encodeURI(songUrl) }), {
          status: 200,
          headers: { 'content-type': 'application/json;charset=UTF-8' },
        }));
      } else {
        return withCors(new Response(JSON.stringify({ code: 404, msg: 'song url not found' }), {
          status: 404,
          headers: { 'content-type': 'application/json;charset=UTF-8' },
        }));
      }
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
  }

  if (url.pathname === '/api/lyric') {
    const songId = url.searchParams.get('songId');
    if (!songId) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing songId' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
    const targetUrl = `https://netease-cloud-music-api.vercel.app/lyric?id=${songId}`;
    try {
      const resp = await fetch(targetUrl);
      const contentType = resp.headers.get('content-type') || 'application/json';
      const body = await resp.text();
      return withCors(new Response(body, {
        status: resp.status,
        headers: { 'content-type': contentType },
      }));
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
  }

  if (url.pathname === '/') {
    return withCors(new Response(htmlContent, {
      headers: { 'content-type': 'text/html;charset=UTF-8' },
    }))
  }

  return withCors(new Response('Not Found', { status: 404 }))
}

const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>网易云音乐播放器</title>
<script src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.2.4/howler.min.js"></script>
<style>
  body {
    background: #181d21;
    color: #e6e6e6;
    font-family: "Segoe UI", "微软雅黑", sans-serif;
    margin:0;
    padding:0;
  }
  .container {
    max-width: 700px;
    margin: 40px auto 0 auto;
    background: #22272b;
    border-radius: 8px;
    box-shadow: 0 2px 18px #0004;
    padding: 30px 35px 25px 35px;
  }
  h1, h2 {
    text-align: center;
    margin: 0 0 18px 0;
    letter-spacing: 1px;
    color: #e6e6e6;
  }
  #searchBar {
    text-align:center;
    margin-bottom: 18px;
  }
  #searchInput {
    width: 350px;
    padding: 7px 8px;
    font-size: 16px;
    border-radius: 4px;
    border: none;
    background: #282c34;
    color: #ccc;
  }
  #searchBtn {
    padding: 7px 18px;
    font-size: 16px;
    margin-left: 14px;
    cursor: pointer;
    border: none;
    border-radius: 4px;
    background: #333c45;
    color: #d4d4d4;
  }
  #searchBtn:hover {
    background: #00d4ff;
    color: #222;
  }
  #loading {
    text-align:center;
    margin: 10px 0;
    font-weight: 600;
    color: #e6e6e6;
  }
  #resultList {
    margin: 12px 0 0 0;
    border-radius: 6px;
    background: #23292f;
    list-style: none;
    padding-left: 0;
    max-height: 300px;
    overflow-y: auto;
  }
  #resultList li {
    display:flex;
    justify-content:space-between;
    align-items:center;
    padding:10px 15px;
    border-bottom: 1px solid #353c41;
    font-size: 15px;
    cursor: default;
  }
  #resultList li:last-child {
    border-bottom: none;
  }
  #resultList li:hover {
    background: #2a3038;
  }
  .song-info {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 66%;
    color: #e6e6e6;
    flex: 1;
    cursor: pointer;
  }
  .song-info b {
    font-weight: 600;
    margin-right: 6px;
  }
  .song-actions {
    margin-left: 8px;
    display: flex;
    gap: 8px;
  }
  .song-actions button {
    padding: 6px 13px;
    font-size: 14px;
    border: none;
    border-radius: 4px;
    background: #333c45;
    color: #d4d4d4;
    cursor: pointer;
  }
  .song-actions button:hover {
    background: #00d4ff;
    color: #222;
  }
  #pagination {
    margin-top: 12px;
    text-align:center;
    font-size: 15px;
    display: flex;
    justify-content: center;
    gap: 10px;
    color: #e6e6e6;
  }
  #pagination button {
    padding: 6px 13px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    background: #333c45;
    color: #d4d4d4;
    cursor: pointer;
  }
  #pagination button:disabled {
    opacity: 0.45;
    cursor: not-allowed;
  }
  #pagination button:not(:disabled):hover {
    background: #00d4ff;
    color: #222;
  }

  /* 歌单相关样式 */
  #userPlaylistSongs {
    margin: 12px 0 0 0;
    border-radius: 6px;
    background: #23292f;
    list-style: none;
    padding-left: 0;
    max-height: 300px;
    overflow-y: auto;
  }
  #userPlaylistSongs li {
    display:flex;
    justify-content:space-between;
    align-items:center;
    padding:10px 15px;
    border-bottom: 1px solid #353c41;
    font-size: 15px;
  }
  #userPlaylistSongs li:last-child {
    border-bottom: none;
  }
  #userPlaylistSongs li:hover {
    background: #2a3038;
  }
  #userPlaylistSongs li.playing {
    background: rgba(0, 212, 255, 0.1) !important;
    border-left: 3px solid #00d4ff !important;
    color: #00d4ff !important;
  }

  /* 播放器相关样式 */
  .player-section {
    background: #1b2128;
    border-radius: 8px;
    box-shadow: 0 2px 10px #111;
    padding: 18px 18px 10px 18px;
    margin: 24px 0 10px 0;
  }
  #nowPlaying {
    font-weight: bold;
    font-size: 15px;
    margin-bottom: 8px;
    color: #eee;
    text-align: center;
  }
  .control-buttons button {
    font-size: 18px;
    margin: 0 7px;
    background: #444;
    border: none;
    color: #fff;
    border-radius: 4px;
    padding: 5px 12px;
    cursor:pointer;
  }
  .control-buttons button:hover {
    background: #00d4ff;
    color: #222;
  }
  .progress-container {
    width:100%;
    height:8px;
    background:#555;
    border-radius:4px;
    margin:10px 0;
    cursor:pointer;
  }
  .progress-bar {
    height:100%;
    width:0;
    background:#00d4ff;
    border-radius:4px;
    transition: width 0.2s;
  }
  .time-display {
    margin-left: 14px;
    font-size: 13px;
    color: #bbb;
    display: flex;
    justify-content: space-between;
  }
  #lyricBox {
    background:#23292f;
    padding:15px 10px 15px 20px;
    min-height:60px;
    line-height:2;
    font-size:15px;
    border-radius:6px;
    margin-bottom:16px;
    max-height:120px;
    overflow:auto;
    color:#ccc;
    text-align: center;
  }
  #lyricBox .current {
    color:#00d4ff;
    font-weight:bold;
    background:rgba(0,212,255,0.07);
  }
</style>
</head>
<body>
<div class="container">
  <h2>网易云音乐播放器</h2>
  <div id="searchBar">
    <input type="text" id="searchInput" placeholder="请输入歌曲关键词" autocomplete="off" />
    <button id="searchBtn">搜索</button>
  </div>
  <div id="loading" style="display:none;">搜索中...</div>
  <ul id="resultList"></ul>
  <div id="pagination" style="display:none;">
    <button id="prevBtn">上一页</button>
    <span id="pageInfo"></span>
    <button id="nextBtn">下一页</button>
  </div>

  <div class="player-section" style="display:none;" id="custom-player">
    <div id="nowPlaying">
      <span id="current-title"></span> - <span id="current-artist"></span>
    </div>
    <div class="control-buttons">
      <button id="prevSongBtn">上一曲</button>
      <button id="playPauseBtn">播放</button>
      <button id="nextSongBtn">下一曲</button>
      <span class="time-display" id="timeDisplay">00:00 / 00:00</span>
    </div>
    <div class="progress-container" id="progressContainer">
      <div class="progress-bar" id="progressBar"></div>
    </div>
  </div>

  <div id="lyricBox"></div>

  <h2>我的歌单</h2>
  <ul id="userPlaylistSongs"></ul>
</div>

<script>
const Howl = window.Howl;

(() => {
  const searchInput = document.getElementById('searchInput');
  const searchBtn = document.getElementById('searchBtn');
  const loading = document.getElementById('loading');
  const resultList = document.getElementById('resultList');
  const prevBtn = document.getElementById('prevBtn');
  const nextBtn = document.getElementById('nextBtn');
  const pageInfo = document.getElementById('pageInfo');
  const userPlaylistSongs = document.getElementById('userPlaylistSongs');
  const prevSongBtn = document.getElementById('prevSongBtn');
  const nextSongBtn = document.getElementById('nextSongBtn');
  const playPauseBtn = document.getElementById('playPauseBtn');
  const nowPlaying = document.getElementById('nowPlaying');
  const progressContainer = document.getElementById('progressContainer');
  const progressBar = document.getElementById('progressBar');
  const timeDisplay = document.getElementById('timeDisplay');
  const lyricBox = document.getElementById('lyricBox');
  const customPlayer = document.getElementById('custom-player');
  const titleElem = document.getElementById('current-title');
  const artistElem = document.getElementById('current-artist');

  let sound = null;
  let progressInterval = null;
  let lyricTimer = null;
  let lyricArr = [];

  let currentPage = 1;
  const pageSize = 10;
  let totalCount = 0;
  let currentKeyword = '';
  let searchResults = [];
  let userPlaylist = JSON.parse(localStorage.getItem('userPlaylist') || '[]');
  let currentPlaylist = [];
  let currentSongIndex = -1;
  let isPlaying = false;
  let isUserScrolling = false;
  let userScrollTimer = null;
  let isAutoScrolling = false;
  let isPageScrolling = false;
  let pageScrollTimeout = null;

  

  function saveUserPlaylist(){
    localStorage.setItem('userPlaylist', JSON.stringify(userPlaylist));
  }

  function renderUserPlaylist(){
    userPlaylistSongs.innerHTML = '';
    if(userPlaylist.length === 0){
      userPlaylistSongs.innerHTML = '<li style="padding:10px;color:#aaa;">歌单空空如也，快去添加喜欢的歌曲吧</li>';
      return;
    }
    userPlaylist.forEach((song,i)=>{
      const li = document.createElement('li');
      li.className = (currentPlaylist === userPlaylist && currentSongIndex === i) ? 'playing' : '';

      const info = document.createElement('div');
      info.className = 'song-info';
      const artists = song.artists.map(a => a.name).join(', ');
      const albumName = song.album?.name || song.al?.name || '';
      const artistWithAlbum = albumName ? \`\${artists} (\${albumName})\` : artists;
      info.innerHTML = \`<b>\${song.name}</b> - \${artistWithAlbum}\`;
      info.title = \`\${song.name} — \${artistWithAlbum}\`;
      info.onclick = () => {
        currentPlaylist = userPlaylist;
        playSong(i);
      }

      const actions = document.createElement('div');
      actions.className = 'song-actions';

      const playBtn = document.createElement('button');
      playBtn.textContent = '播放';
      playBtn.onclick = (e) => {
        e.stopPropagation();
        currentPlaylist = userPlaylist;
        playSong(i);
      };

      const delBtn = document.createElement('button');
      delBtn.textContent = '删除';
      delBtn.style.background = '#dc3545';
      delBtn.onclick = (e) => {
        e.stopPropagation();
        userPlaylist.splice(i,1);
        saveUserPlaylist();
        renderUserPlaylist();
        if(currentPlaylist === userPlaylist){
          if(currentSongIndex === i){
            if (sound) sound.pause();
            titleElem.textContent = '';
            artistElem.textContent = '';
            customPlayer.style.display = 'none';
            currentSongIndex = -1;
            clearLyric();
          } else if(currentSongIndex > i){
            currentSongIndex--;
          }
        }
      };

      actions.appendChild(playBtn);
      actions.appendChild(delBtn);

      li.appendChild(info);
      li.appendChild(actions);

      userPlaylistSongs.appendChild(li);
    });
  }

  function addToUserPlaylist(song){
    if(userPlaylist.find(s=>s.id === song.id)){
      showMessage('歌曲已在歌单中', 'warning');
      return;
    }
    userPlaylist.push(song);
    saveUserPlaylist();
    renderUserPlaylist();
    showMessage('已添加到歌单', 'success');
  }

  function showMessage(message, type = 'info') {
    // 创建消息提示元素
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.cssText = \`
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 10px 15px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      z-index: 1000;
      transition: opacity 0.3s;
    \`;

    // 根据类型设置颜色（暗黑主题）
    switch (type) {
      case 'success':
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #00d4ff';
        break;
      case 'warning':
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #666';
        messageDiv.style.color = '#e6e6e6';
        break;
      case 'error':
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #666';
        break;
      default:
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #00d4ff';
    }

    document.body.appendChild(messageDiv);

    // 3秒后自动消失
    setTimeout(() => {
      messageDiv.style.opacity = '0';
      setTimeout(() => {
        if (messageDiv.parentNode) {
          messageDiv.parentNode.removeChild(messageDiv);
        }
      }, 300);
    }, 3000);
  }

  function renderSearchResults(){
    resultList.innerHTML = '';
    if(searchResults.length === 0){
      resultList.innerHTML = '<li style="padding:10px;color:#aaa;">暂无搜索结果</li>';
      return;
    }
    searchResults.forEach((song,i)=>{
      const li = document.createElement('li');
      li.className = (currentPlaylist === searchResults && currentSongIndex === i) ? 'playing' : '';

      const info = document.createElement('div');
      info.className = 'song-info';
      const artists = song.artists.map(a => a.name).join(', ');
      const albumName = song.album?.name || song.al?.name || '';
      const artistWithAlbum = albumName ? \`\${artists} (\${albumName})\` : artists;
      info.innerHTML = \`<b>\${song.name}</b> - \${artistWithAlbum}\`;
      info.title = \`\${song.name} — \${artistWithAlbum}\`;
      info.onclick = () => {
        currentPlaylist = searchResults;
        playSong(i);
      }

      const actions = document.createElement('div');
      actions.className = 'song-actions';

      const playBtn = document.createElement('button');
      playBtn.textContent = '播放';
      playBtn.onclick = (e) => {
        e.stopPropagation();
        currentPlaylist = searchResults;
        playSong(i);
      };

      const addBtn = document.createElement('button');
      addBtn.textContent = '加入歌单';
      addBtn.onclick = (e) => {
        e.stopPropagation();
        addToUserPlaylist(song);
      };

      actions.appendChild(playBtn);
      actions.appendChild(addBtn);

      li.appendChild(info);
      li.appendChild(actions);

      resultList.appendChild(li);
    });
  }

  function updatePagination(){
    const pagination = document.getElementById('pagination');
    if (searchResults.length > 0) {
      pagination.style.display = 'block';
      prevBtn.disabled = currentPage === 1;
      nextBtn.disabled = currentPage * pageSize >= totalCount;
      pageInfo.textContent = \`第 \${currentPage} 页\`;
    } else {
      pagination.style.display = 'none';
    }
  }

  function fetchSongs(keyword,page=1){
    if(!keyword) return;
    loading.style.display = 'block';
    resultList.innerHTML = '';
    prevBtn.disabled = true;
    nextBtn.disabled = true;
    fetch(\`/api/search?s=\${encodeURIComponent(keyword)}&offset=\${(page-1)*pageSize}&limit=\${pageSize}\`).then(res=>{
      if(!res.ok) throw new Error('网络错误');
      return res.json();
    }).then(data=>{
      loading.style.display = 'none';
      if(!data.result || !data.result.songs || data.result.songs.length === 0){
        searchResults = [];
        totalCount = 0;
        currentSongIndex = -1;
        renderSearchResults();
        updatePagination();
        return;
      }
      searchResults = data.result.songs;
      totalCount = data.result.songCount || 0;
      currentPage = page;
      renderSearchResults();
      updatePagination();
    }).catch(err=>{
      loading.style.display = 'none';
      showMessage('搜索失败：' + err.message, 'error');
    });
  }

  function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return \`\${mins.toString().padStart(2, '0')}:\${secs.toString().padStart(2, '0')}\`;
  }

  function updateProgress() {
    if (!sound || !sound.playing()) return;

    const duration = sound.duration() || 0;
    const position = sound.seek() || 0;
    const progress = duration ? (position / duration) * 100 : 0;

    progressBar.style.width = \`\${progress}%\`;

    const currentTime = formatTime(position);
    const totalTime = formatTime(duration);
    timeDisplay.textContent = \`\${currentTime} / \${totalTime}\`;
  }

  function clearLyric() {
    if (lyricTimer) clearInterval(lyricTimer);
    lyricBox.innerHTML = '';
    lyricArr = [];
  }

  function parseLRC(lrc) {
    if (!lrc || typeof lrc !== 'string' || lrc.trim().length === 0) return [];
    const lines = lrc.split(/\\r?\\n/);
    const res = [];
    
    lines.forEach(line => {
      // 匹配时间标签 [mm:ss.xxx] 或 [mm:ss]
      const timeMatch = line.match(/\\[(\\d{1,2}):(\\d{2})\\.?(\\d{0,3})?\\]/);
      if (timeMatch) {
        const min = parseInt(timeMatch[1], 10);
        const sec = parseInt(timeMatch[2], 10);
        const ms = timeMatch[3] ? parseInt(timeMatch[3].padEnd(3, '0'), 10) : 0;
        const time = min * 60 + sec + ms / 1000;
        
        // 获取歌词文本，移除所有时间标签
        const lrcText = line.replace(/\\[\\d{1,2}:\\d{2}\\.?\\d{0,3}?\\]/g, '').trim();
        
        if (lrcText) {
          res.push({ time, lrc: lrcText });
        }
      }
    });
    
    // 按时间排序并去重
    res.sort((a, b) => a.time - b.time);
    
    // 去除重复的歌词（相同时间和文本）
    const uniqueLyrics = [];
    for (let i = 0; i < res.length; i++) {
      const current = res[i];
      const prev = uniqueLyrics[uniqueLyrics.length - 1];
      
      if (!prev || prev.lrc !== current.lrc || Math.abs(prev.time - current.time) > 0.1) {
        uniqueLyrics.push(current);
      }
    }
    
    return uniqueLyrics;
  }

  function renderLyricPanel() {
    if (!lyricArr || lyricArr.length === 0) {
      lyricBox.innerHTML = "<div style='color:#888; text-align:center; padding:20px;'>（无歌词）</div>";
      return;
    }
    
    const lyricHTML = lyricArr.map((item, index) => 
      \`<div class="lyric-line" data-index="\${index}" data-time="\${item.time}">\${item.lrc}</div>\`
    ).join('');
    
    lyricBox.innerHTML = lyricHTML;
  }

  // 优化歌词同步与滚动
  function syncLyric() {
    if (!sound || !lyricArr.length) return;
    if (isUserScrolling) return;
  
    const currentTime = sound.seek() || 0;
    let activeIndex = -1;
  
    for (let i = 0; i < lyricArr.length; i++) {
      if (currentTime >= lyricArr[i].time) {
        activeIndex = i;
      } else {
        break;
      }
    }
  
    const lyricLines = lyricBox.querySelectorAll('.lyric-line');
    lyricLines.forEach((line, index) => {
      line.classList.toggle('current', index === activeIndex);
      if (index === activeIndex && !isUserScrolling) {
        isAutoScrolling = true;
        // 手动计算滚动位置
        const rectLyricBox = lyricBox.getBoundingClientRect();
        const rectLine = line.getBoundingClientRect();
        const scrollTop = lyricBox.scrollTop + rectLine.top - rectLyricBox.top - (lyricBox.clientHeight - rectLine.height) / 2;
  
        // 使用平滑滚动动画
        smoothScroll(lyricBox, scrollTop, 400);
        setTimeout(() => { 
          isAutoScrolling = false; 
        }, 400);
      }
    });
  }

  // 平滑滚动函数
  function smoothScroll(element, targetScrollTop, duration) {
    const startScrollTop = element.scrollTop;
    const distance = targetScrollTop - startScrollTop;
    const startTime = performance.now();

    function animation(currentTime) {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);

      // 使用缓动函数
      const easeInOutQuad = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      element.scrollTop = startScrollTop + distance * easeInOutQuad;

      if (progress < 1) {
        requestAnimationFrame(animation);
      }
    }

    requestAnimationFrame(animation);
  }

  function playSong(index){
    if(index<0 || index>=currentPlaylist.length) return;
    currentSongIndex = index;
    const song = currentPlaylist[index];
    const qualities = ['jymaster', 'jyeffect', 'flac', '320k'];
    let qualityIndex = 0;

    function tryNextQuality() {
      if (qualityIndex >= qualities.length) {
        showMessage('所有音质尝试失败，无法获取歌曲播放地址', 'error');
        clearLyric();
        return;
      }
      const quality = qualities[qualityIndex];
      fetch(\`/api/get-song-url?songId=\${song.id}&quality=\${quality}\`)
        .then(res => {
          if (!res.ok) throw new Error('获取播放地址失败');
          return res.json();
        })
        .then(data => {
          if (data.code === 200) {
            if (sound) sound.unload();
            if (progressInterval) clearInterval(progressInterval);
            clearLyric();

            sound = new Howl({
              src: [data.data],
              html5: true,
              onplay: () => {
                isPlaying = true;
                playPauseBtn.textContent = '暂停';
                updateNowPlaying();
                updateProgress();
                progressInterval = setInterval(updateProgress, 1000);
                if (lyricArr.length > 0) {
                  if (lyricTimer) clearInterval(lyricTimer);
                  lyricTimer = setInterval(syncLyric, 300);
                }
              },
              onpause: () => {
                isPlaying = false;
                playPauseBtn.textContent = '播放';
                if (lyricTimer) clearInterval(lyricTimer);
              },
              onend: () => {
                clearInterval(progressInterval);
                if (lyricTimer) clearInterval(lyricTimer);
                clearLyric();
                if(currentSongIndex < currentPlaylist.length -1){
                  playSong(currentSongIndex +1);
                }else{
                  titleElem.textContent = '';
                  artistElem.textContent = '';
                  playPauseBtn.textContent = '播放';
                  isPlaying = false;
                  progressBar.style.width = '0%';
                  timeDisplay.textContent = '00:00 / 00:00';
                }
              },
              onloaderror: (id, error) => {
                qualityIndex++;
                tryNextQuality();
              },
              onplayerror: (id, error) => {
                qualityIndex++;
                tryNextQuality();
              }
            });

            // 歌词加载
            lyricBox.innerHTML = '歌词加载中...';
            fetch(\`/api/lyric?songId=\${song.id}\`)
              .then(res => res.json())
              .then(data => {
                let lrc = '';
                if(data.lrc && data.lrc.lyric) lrc = data.lrc.lyric;
                else if (data.tlyric && data.tlyric.lyric) lrc = data.tlyric.lyric;
                if (lrc && lrc.trim()) {
                  lyricArr = parseLRC(lrc);
                  renderLyricPanel();
                  if (lyricTimer) clearInterval(lyricTimer);
                  lyricTimer = setInterval(syncLyric, 300);
                } else {
                  lyricBox.innerHTML = "<span style='color:#888;'>（无歌词）</span>";
                }
              }).catch(()=>{
                lyricBox.innerHTML = "<span style='color:#888;'>（歌词加载失败）</span>";
              });

            sound.play();
            updatePlayingHighlight();
          } else {
            qualityIndex++;
            tryNextQuality();
          }
        })
        .catch(err => {
          qualityIndex++;
          tryNextQuality();
        });
    }

    tryNextQuality();
  }

  function updateNowPlaying(){
    if(currentSongIndex<0 || currentSongIndex>=currentPlaylist.length){
      titleElem.textContent = '';
      artistElem.textContent = '';
      customPlayer.style.display = 'none';
      return;
    }
    const song = currentPlaylist[currentSongIndex];
    titleElem.textContent = song.name;
    artistElem.textContent = song.artists.map(a=>a.name).join(', ');
    customPlayer.style.display = 'block';
    updatePlayingHighlight();
  }

  function updatePlayingHighlight() {
    // 清除所有高亮
    document.querySelectorAll('#resultList li, #userPlaylistSongs li').forEach(el => {
      el.classList.remove('playing');
    });

    if (currentSongIndex < 0 || currentSongIndex >= currentPlaylist.length) return;

    // 高亮当前播放的歌曲
    if (currentPlaylist === searchResults) {
      const searchItems = document.querySelectorAll('#resultList li');
      if (searchItems[currentSongIndex]) {
        searchItems[currentSongIndex].classList.add('playing');
      }
    } else if (currentPlaylist === userPlaylist) {
      const playlistItems = document.querySelectorAll('#userPlaylistSongs li');
      if (playlistItems[currentSongIndex]) {
        playlistItems[currentSongIndex].classList.add('playing');
      }
    }
  }

  searchBtn.onclick = () => {
    const kw = searchInput.value.trim();
    if(!kw){
      showMessage('请输入关键词', 'warning');
      return;
    }
    currentKeyword = kw;
    currentPage = 1;
    fetchSongs(kw,1);
  }

  searchInput.addEventListener('keydown', (e) => {
    if(e.key === 'Enter'){
      searchBtn.click();
    }
  });

  prevBtn.onclick = () => {
    if(currentPage>1){
      fetchSongs(currentKeyword,currentPage-1);
    }
  }
  nextBtn.onclick = () => {
    if(currentPage*pageSize<totalCount){
      fetchSongs(currentKeyword,currentPage+1);
    }
  }

  playPauseBtn.onclick = () => {
    if (!sound) return;
    if (sound.playing()) {
      sound.pause();
      clearInterval(progressInterval);
      if (lyricTimer) clearInterval(lyricTimer);
    } else {
      sound.play();
      progressInterval = setInterval(updateProgress, 1000);
      if (lyricArr.length > 0 && !lyricTimer)
        lyricTimer = setInterval(syncLyric, 300);
    }
  }

  prevSongBtn.onclick = () => {
    if(currentSongIndex > 0){
      playSong(currentSongIndex -1);
    } else {
      showMessage('已经是第一首歌了', 'info');
    }
  }

  nextSongBtn.onclick = () => {
    if(currentSongIndex < currentPlaylist.length -1){
      playSong(currentSongIndex +1);
    } else {
      showMessage('已经是最后一首歌了', 'info');
    }
  }

  progressContainer.addEventListener('click', (e) => {
    if (!sound || !sound.duration()) return;
    const rect = progressContainer.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const duration = sound.duration();
    sound.seek(duration * clickPosition);
    updateProgress();
    syncLyric();
  });

  window.addEventListener('scroll', function() {
    isPageScrolling = true;
    clearTimeout(pageScrollTimeout);
    pageScrollTimeout = setTimeout(() => {
      isPageScrolling = false;
    }, 15000);
  });
  
  lyricBox.addEventListener('scroll', () => {
    if (isAutoScrolling) return;
    isUserScrolling = true;
    if (userScrollTimer) clearTimeout(userScrollTimer);
    userScrollTimer = setTimeout(() => {
      isUserScrolling = false;
    }, 200);
  });


  // 阻止歌词容器滚动时触发页面滚动
  lyricBox.addEventListener('wheel', (e) => {
    const container = lyricBox;
    const { deltaY } = e;
    // 计算容器剩余可滚动的距离
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtTop = deltaY < 0 && scrollTop === 0;
    const isAtBottom = deltaY > 0 && scrollTop + clientHeight >= scrollHeight;

    // 当滚动到容器顶部或底部时，允许页面滚动
    if (isAtTop || isAtBottom) {
      return;
    }

    // 阻止默认的页面滚动行为
    e.preventDefault();
    // 手动滚动歌词容器
    container.scrollTop += deltaY;
  }, { passive: false });

  renderUserPlaylist();
  })();

  
</script>
</body>
</html>`;
