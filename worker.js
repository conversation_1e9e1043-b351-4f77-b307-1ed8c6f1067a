addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

const SONG_URL_API = 'https://88.lxmusic.xn--fiqs8s/lxmusicv3/url/wy';

function withCors(response) {
  const newHeaders = new Headers(response.headers)
  newHeaders.set('Access-Control-Allow-Origin', '*')
  newHeaders.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  newHeaders.set('Access-Control-Allow-Headers', 'Content-Type')
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  })
}

async function handleRequest(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    })
  }

  const url = new URL(request.url)

  if (url.pathname === '/api/search') {
    const keyword = url.searchParams.get('keywords') || url.searchParams.get('s') || ''
    const limit = url.searchParams.get('limit') || '10'
    const offset = url.searchParams.get('offset') || '0'

    if (!keyword) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing keywords' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }))
    }

    const targetUrl = `https://netease-cloud-music-api.vercel.app/search?keywords=${encodeURIComponent(keyword)}&limit=${limit}&offset=${offset}`

    try {
      const resp = await fetch(targetUrl)
      const contentType = resp.headers.get('content-type') || 'application/json'
      const body = await resp.text()
      return withCors(new Response(body, {
        status: resp.status,
        headers: { 'content-type': contentType },
      }))
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }))
    }
  }

  if (url.pathname === '/api/get-song-url') {
    const songId = url.searchParams.get('songId');
    const quality = url.searchParams.get('quality') || 'flac';
    if (!songId) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing songId' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
    const targetUrl = `${SONG_URL_API}/${songId}/${quality}`;
    try {
      const resp = await fetch(targetUrl);
      const data = await resp.json();
      if (data.data) {
        const songUrl = data.data.replace(/\\/g, '');
        return withCors(new Response(JSON.stringify({ code: 200, data: encodeURI(songUrl) }), {
          status: 200,
          headers: { 'content-type': 'application/json;charset=UTF-8' },
        }));
      } else {
        return withCors(new Response(JSON.stringify({ code: 404, msg: 'song url not found' }), {
          status: 404,
          headers: { 'content-type': 'application/json;charset=UTF-8' },
        }));
      }
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
  }

  if (url.pathname === '/api/lyric') {
    const songId = url.searchParams.get('songId');
    if (!songId) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing songId' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
    const targetUrl = `https://netease-cloud-music-api.vercel.app/lyric?id=${songId}`;
    try {
      const resp = await fetch(targetUrl);
      const contentType = resp.headers.get('content-type') || 'application/json';
      const body = await resp.text();
      return withCors(new Response(body, {
        status: resp.status,
        headers: { 'content-type': contentType },
      }));
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
  }

  if (url.pathname === '/api/playlist') {
    const playlistId = url.searchParams.get('id');
    if (!playlistId) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing playlist id' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }

    // 首先获取歌单基本信息
    const detailUrl = `https://netease-cloud-music-api.vercel.app/playlist/detail?id=${encodeURIComponent(playlistId)}`;
    try {
      const detailResp = await fetch(detailUrl);
      const detailData = await detailResp.json();

      if (detailData.code !== 200 || !detailData.playlist) {
        return withCors(new Response(JSON.stringify({ code: 404, msg: 'playlist not found or private' }), {
          status: 404,
          headers: { 'content-type': 'application/json;charset=UTF-8' },
        }));
      }

      const playlist = detailData.playlist;
      const totalCount = playlist.trackCount || 0;

      // 获取完整的歌曲列表，使用 track/all 接口
      const tracksUrl = `https://netease-cloud-music-api.vercel.app/playlist/track/all?id=${encodeURIComponent(playlistId)}&limit=${Math.min(totalCount, 500)}`;
      const tracksResp = await fetch(tracksUrl);
      const tracksData = await tracksResp.json();

      let songs = [];
      if (tracksData.code === 200 && tracksData.songs) {
        songs = tracksData.songs;
      } else {
        // 如果 track/all 接口失败，回退到使用 detail 接口的 tracks
        songs = playlist.tracks || [];
      }

      // 格式化歌单信息
      const result = {
        code: 200,
        name: playlist.name || '未知歌单',
        description: playlist.description || '',
        songCount: totalCount,
        songs: songs.map(song => ({
          id: song.id,
          name: song.name || '未知歌曲',
          artists: song.ar || song.artists || [{ name: '未知艺术家' }],
          album: song.al || song.album || { name: '未知专辑' }
        }))
      };

      return withCors(new Response(JSON.stringify(result), {
        status: 200,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    } catch (err) {
      console.error('Playlist API error:', err);
      return withCors(new Response(JSON.stringify({ code: 500, msg: `获取歌单失败: ${err.message}` }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
  }

  if (url.pathname === '/') {
    return withCors(new Response(htmlContent, {
      headers: { 'content-type': 'text/html;charset=UTF-8' },
    }))
  }

  return withCors(new Response('Not Found', { status: 404 }))
}

const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>网易云音乐播放器</title>
<script src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.2.4/howler.min.js"></script>
<style>
  body {
    background: #181d21;
    color: #e6e6e6;
    font-family: "Segoe UI", "微软雅黑", sans-serif;
    margin:0;
    padding:0;
  }
  .container {
    max-width: 700px;
    margin: 40px auto 0 auto;
    background: #22272b;
    border-radius: 8px;
    box-shadow: 0 2px 18px #0004;
    padding: 30px 35px 25px 35px;
  }
  h1, h2 {
    text-align: center;
    margin: 0 0 18px 0;
    letter-spacing: 1px;
    color: #e6e6e6;
  }
  #searchBar {
    text-align:center;
    margin-bottom: 18px;
  }
  #searchInput {
    width: 350px;
    padding: 7px 8px;
    font-size: 16px;
    border-radius: 4px;
    border: none;
    background: #282c34;
    color: #ccc;
  }
  #searchBtn {
    padding: 7px 18px;
    font-size: 16px;
    margin-left: 14px;
    cursor: pointer;
    border: none;
    border-radius: 4px;
    background: #333c45;
    color: #d4d4d4;
  }
  #searchBtn:hover {
    background: #00d4ff;
    color: #222;
  }
  #loading {
    text-align:center;
    margin: 10px 0;
    font-weight: 600;
    color: #e6e6e6;
  }
  #resultList {
    margin: 12px 0 0 0;
    border-radius: 6px;
    background: #23292f;
    list-style: none;
    padding-left: 0;
    max-height: 300px;
    overflow-y: auto;
  }
  #resultList li {
    display:flex;
    justify-content:space-between;
    align-items:center;
    padding:10px 15px;
    border-bottom: 1px solid #353c41;
    font-size: 15px;
    cursor: default;
  }
  #resultList li:last-child {
    border-bottom: none;
  }
  #resultList li:hover {
    background: #2a3038;
  }
  .song-info {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 66%;
    color: #e6e6e6;
    flex: 1;
    cursor: pointer;
  }
  .song-info b {
    font-weight: 600;
    margin-right: 6px;
  }
  .song-actions {
    margin-left: 8px;
    display: flex;
    gap: 8px;
  }
  .song-actions button {
    padding: 6px 13px;
    font-size: 14px;
    border: none;
    border-radius: 4px;
    background: #333c45;
    color: #d4d4d4;
    cursor: pointer;
  }
  .song-actions button:hover {
    background: #00d4ff;
    color: #222;
  }
  #pagination {
    margin-top: 12px;
    text-align:center;
    font-size: 15px;
    display: flex;
    justify-content: center;
    gap: 10px;
    color: #e6e6e6;
  }
  #pagination button {
    padding: 6px 13px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    background: #333c45;
    color: #d4d4d4;
    cursor: pointer;
  }
  #pagination button:disabled {
    opacity: 0.45;
    cursor: not-allowed;
  }
  #pagination button:not(:disabled):hover {
    background: #00d4ff;
    color: #222;
  }

  /* 歌单相关样式 */
  #userPlaylistSongs {
    margin: 12px 0 0 0;
    border-radius: 6px;
    background: #23292f;
    list-style: none;
    padding-left: 0;
    max-height: 300px;
    overflow-y: auto;
  }
  #userPlaylistSongs li {
    display:flex;
    justify-content:space-between;
    align-items:center;
    padding:10px 15px;
    border-bottom: 1px solid #353c41;
    font-size: 15px;
  }
  #userPlaylistSongs li:last-child {
    border-bottom: none;
  }
  #userPlaylistSongs li:hover {
    background: #2a3038;
  }
  #userPlaylistSongs li.playing {
    background: rgba(0, 212, 255, 0.1) !important;
    border-left: 3px solid #00d4ff !important;
    color: #00d4ff !important;
  }

  /* 播放器相关样式 */
  .player-section {
    background: #1b2128;
    border-radius: 8px;
    box-shadow: 0 2px 10px #111;
    padding: 18px 18px 10px 18px;
    margin: 24px 0 10px 0;
  }
  #nowPlaying {
    font-weight: bold;
    font-size: 15px;
    margin-bottom: 8px;
    color: #eee;
    text-align: center;
  }
  .control-buttons button {
    font-size: 18px;
    margin: 0 7px;
    background: #444;
    border: none;
    color: #fff;
    border-radius: 4px;
    padding: 5px 12px;
    cursor:pointer;
  }
  .control-buttons button:hover {
    background: #00d4ff;
    color: #222;
  }
  .progress-container {
    width:100%;
    height:8px;
    background:#555;
    border-radius:4px;
    margin:10px 0;
    cursor:pointer;
  }
  .progress-bar {
    height:100%;
    width:0;
    background:#00d4ff;
    border-radius:4px;
    transition: width 0.2s;
  }
  .time-display {
    margin-left: 14px;
    font-size: 13px;
    color: #bbb;
    display: flex;
    justify-content: space-between;
  }
  #lyricBox {
    background:#23292f;
    padding:15px 10px 15px 20px;
    min-height:60px;
    line-height:2;
    font-size:15px;
    border-radius:6px;
    margin-bottom:16px;
    max-height:120px;
    overflow:auto;
    color:#ccc;
    text-align: center;
  }
  #lyricBox .current {
    color:#00d4ff;
    font-weight:bold;
    background:rgba(0,212,255,0.07);
  }

  /* 导入歌单样式 */
  #import-playlist-section {
    margin: 10px 0;
    padding: 15px;
    background: #23292f;
    border-radius: 6px;
    border: 1px solid #353c41;
  }
  #import-playlist-section label {
    color: #e6e6e6;
    font-size: 14px;
    font-weight: 500;
  }
  #playlist-id-input {
    flex: 1;
    padding: 7px 8px;
    border: 1px solid #555;
    background: #282c34;
    color: #ccc;
    border-radius: 4px;
    font-size: 14px;
  }
  #playlist-id-input:focus {
    outline: none;
    border-color: #00d4ff;
  }
  #import-playlist-btn {
    padding: 7px 15px;
    background: #333c45;
    color: #d4d4d4;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
  }
  #import-playlist-btn:hover {
    background: #00d4ff;
    color: #222;
  }
  #import-playlist-btn:disabled {
    background: #555;
    color: #999;
    cursor: not-allowed;
  }
  #import-status {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
    min-height: 16px;
  }
</style>
</head>
<body>
<div class="container">
  <h2>网易云音乐播放器</h2>
  <div id="searchBar">
    <input type="text" id="searchInput" placeholder="请输入歌曲关键词" autocomplete="off" />
    <button id="searchBtn">搜索</button>
  </div>
  <div id="loading" style="display:none;">搜索中...</div>
  <ul id="resultList"></ul>
  <div id="pagination" style="display:none;">
    <button id="prevBtn">上一页</button>
    <span id="pageInfo"></span>
    <button id="nextBtn">下一页</button>
  </div>

  <div class="player-section" style="display:none;" id="custom-player">
    <div id="nowPlaying">
      <span id="current-title"></span> - <span id="current-artist"></span>
    </div>
    <div class="control-buttons">
      <button id="prevSongBtn">上一曲</button>
      <button id="playPauseBtn">播放</button>
      <button id="nextSongBtn">下一曲</button>
      <span class="time-display" id="timeDisplay">00:00 / 00:00</span>
    </div>
    <div class="progress-container" id="progressContainer">
      <div class="progress-bar" id="progressBar"></div>
    </div>
  </div>

  <div id="lyricBox"></div>

  <h2>我的歌单</h2>

  <!-- 导入歌单区域 -->
  <div id="import-playlist-section">
    <div style="display: flex; align-items: center; gap: 10px;">
      <label for="playlist-id-input">歌单ID:</label>
      <input type="text" id="playlist-id-input" placeholder="输入歌单ID，如：7251076707" />
      <button id="import-playlist-btn">导入歌单</button>
    </div>
    <div id="import-status"></div>
  </div>

  <ul id="userPlaylistSongs"></ul>
</div>

<script>
const Howl = window.Howl;

(() => {
  const searchInput = document.getElementById('searchInput');
  const searchBtn = document.getElementById('searchBtn');
  const loading = document.getElementById('loading');
  const resultList = document.getElementById('resultList');
  const prevBtn = document.getElementById('prevBtn');
  const nextBtn = document.getElementById('nextBtn');
  const pageInfo = document.getElementById('pageInfo');
  const userPlaylistSongs = document.getElementById('userPlaylistSongs');
  const prevSongBtn = document.getElementById('prevSongBtn');
  const nextSongBtn = document.getElementById('nextSongBtn');
  const playPauseBtn = document.getElementById('playPauseBtn');
  const nowPlaying = document.getElementById('nowPlaying');
  const progressContainer = document.getElementById('progressContainer');
  const progressBar = document.getElementById('progressBar');
  const timeDisplay = document.getElementById('timeDisplay');
  const lyricBox = document.getElementById('lyricBox');
  const customPlayer = document.getElementById('custom-player');
  const titleElem = document.getElementById('current-title');
  const artistElem = document.getElementById('current-artist');

  let sound = null;
  let progressInterval = null;
  let lyricTimer = null;
  let lyricArr = [];

  let currentPage = 1;
  const pageSize = 10;
  let totalCount = 0;
  let currentKeyword = '';
  let searchResults = [];
  let userPlaylist = JSON.parse(localStorage.getItem('userPlaylist') || '[]');
  let currentPlaylist = [];
  let currentSongIndex = -1;
  let isPlaying = false;
  let isUserScrolling = false; // 添加用户滚动状态标记
  let scrollTimeout = null; // 滚动超时定时器
  let userScrollTimer = null;
  let isAutoScrolling = false;
  let isPageScrolling = false;
  let pageScrollTimeout = null;

  // 检测歌词面板内的滚动 - 来自 index.html
  lyricBox.addEventListener('scroll', function() {
    isUserScrolling = true;
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      isUserScrolling = false;
    }, 2000); // 2秒后认为用户停止滚动
  });

  function saveUserPlaylist(){
    localStorage.setItem('userPlaylist', JSON.stringify(userPlaylist));
  }

  function renderUserPlaylist(){
    userPlaylistSongs.innerHTML = '';
    if(userPlaylist.length === 0){
      userPlaylistSongs.innerHTML = '<li style="padding:10px;color:#aaa;">歌单空空如也，快去添加喜欢的歌曲吧</li>';
      return;
    }
    userPlaylist.forEach((song,i)=>{
      const li = document.createElement('li');
      li.className = (currentPlaylist === userPlaylist && currentSongIndex === i) ? 'playing' : '';

      const info = document.createElement('div');
      info.className = 'song-info';
      const artists = song.artists.map(a => a.name).join(', ');
      const albumName = song.album?.name || song.al?.name || '';
      const artistWithAlbum = albumName ? \`\${artists} (\${albumName})\` : artists;
      info.innerHTML = \`<b>\${song.name}</b> - \${artistWithAlbum}\`;
      info.title = \`\${song.name} — \${artistWithAlbum}\`;
      info.onclick = () => {
        currentPlaylist = userPlaylist;
        playSong(i);
      }

      const actions = document.createElement('div');
      actions.className = 'song-actions';

      const playBtn = document.createElement('button');
      playBtn.textContent = '播放';
      playBtn.onclick = (e) => {
        e.stopPropagation();
        currentPlaylist = userPlaylist;
        playSong(i);
      };

      const delBtn = document.createElement('button');
      delBtn.textContent = '删除';
      delBtn.style.background = '#dc3545';
      delBtn.onclick = (e) => {
        e.stopPropagation();
        userPlaylist.splice(i,1);
        saveUserPlaylist();
        renderUserPlaylist();
        if(currentPlaylist === userPlaylist){
          if(currentSongIndex === i){
            if (sound) sound.pause();
            titleElem.textContent = '';
            artistElem.textContent = '';
            customPlayer.style.display = 'none';
            currentSongIndex = -1;
            clearLyric();
          } else if(currentSongIndex > i){
            currentSongIndex--;
          }
        }
      };

      actions.appendChild(playBtn);
      actions.appendChild(delBtn);

      li.appendChild(info);
      li.appendChild(actions);

      userPlaylistSongs.appendChild(li);
    });
  }

  function addToUserPlaylist(song){
    if(userPlaylist.find(s=>s.id === song.id)){
      showMessage('歌曲已在歌单中', 'warning');
      return;
    }
    userPlaylist.push(song);
    saveUserPlaylist();
    renderUserPlaylist();
    showMessage('已添加到歌单', 'success');
  }

  function showMessage(message, type = 'info') {
    // 创建消息提示元素
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.cssText = \`
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 10px 15px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      z-index: 1000;
      transition: opacity 0.3s;
    \`;

    // 根据类型设置颜色（暗黑主题）
    switch (type) {
      case 'success':
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #00d4ff';
        break;
      case 'warning':
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #666';
        messageDiv.style.color = '#e6e6e6';
        break;
      case 'error':
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #666';
        break;
      default:
        messageDiv.style.backgroundColor = '#333c45';
        messageDiv.style.border = '1px solid #00d4ff';
    }

    document.body.appendChild(messageDiv);

    // 3秒后自动消失
    setTimeout(() => {
      messageDiv.style.opacity = '0';
      setTimeout(() => {
        if (messageDiv.parentNode) {
          messageDiv.parentNode.removeChild(messageDiv);
        }
      }, 300);
    }, 3000);
  }

  function renderSearchResults(){
    resultList.innerHTML = '';
    if(searchResults.length === 0){
      resultList.innerHTML = '<li style="padding:10px;color:#aaa;">暂无搜索结果</li>';
      return;
    }
    searchResults.forEach((song,i)=>{
      const li = document.createElement('li');
      li.className = (currentPlaylist === searchResults && currentSongIndex === i) ? 'playing' : '';

      const info = document.createElement('div');
      info.className = 'song-info';
      const artists = song.artists.map(a => a.name).join(', ');
      const albumName = song.album?.name || song.al?.name || '';
      const artistWithAlbum = albumName ? \`\${artists} (\${albumName})\` : artists;
      info.innerHTML = \`<b>\${song.name}</b> - \${artistWithAlbum}\`;
      info.title = \`\${song.name} — \${artistWithAlbum}\`;
      info.onclick = () => {
        currentPlaylist = searchResults;
        playSong(i);
      }

      const actions = document.createElement('div');
      actions.className = 'song-actions';

      const playBtn = document.createElement('button');
      playBtn.textContent = '播放';
      playBtn.onclick = (e) => {
        e.stopPropagation();
        currentPlaylist = searchResults;
        playSong(i);
      };

      const addBtn = document.createElement('button');
      addBtn.textContent = '加入歌单';
      addBtn.onclick = (e) => {
        e.stopPropagation();
        addToUserPlaylist(song);
      };

      actions.appendChild(playBtn);
      actions.appendChild(addBtn);

      li.appendChild(info);
      li.appendChild(actions);

      resultList.appendChild(li);
    });
  }

  function updatePagination(){
    const pagination = document.getElementById('pagination');
    if (searchResults.length > 0) {
      pagination.style.display = 'block';
      prevBtn.disabled = currentPage === 1;
      nextBtn.disabled = currentPage * pageSize >= totalCount;
      pageInfo.textContent = \`第 \${currentPage} 页\`;
    } else {
      pagination.style.display = 'none';
    }
  }

  function fetchSongs(keyword,page=1){
    if(!keyword) return;
    loading.style.display = 'block';
    resultList.innerHTML = '';
    prevBtn.disabled = true;
    nextBtn.disabled = true;
    fetch(\`/api/search?s=\${encodeURIComponent(keyword)}&offset=\${(page-1)*pageSize}&limit=\${pageSize}\`).then(res=>{
      if(!res.ok) throw new Error('网络错误');
      return res.json();
    }).then(data=>{
      loading.style.display = 'none';
      if(!data.result || !data.result.songs || data.result.songs.length === 0){
        searchResults = [];
        totalCount = 0;
        currentSongIndex = -1;
        renderSearchResults();
        updatePagination();
        return;
      }
      searchResults = data.result.songs;
      totalCount = data.result.songCount || 0;
      currentPage = page;
      renderSearchResults();
      updatePagination();
    }).catch(err=>{
      loading.style.display = 'none';
      showMessage('搜索失败：' + err.message, 'error');
    });
  }

  function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return \`\${mins.toString().padStart(2, '0')}:\${secs.toString().padStart(2, '0')}\`;
  }

  function updateProgress() {
    if (!sound || !sound.playing()) return;

    const duration = sound.duration() || 0;
    const position = sound.seek() || 0;
    const progress = duration ? (position / duration) * 100 : 0;

    progressBar.style.width = \`\${progress}%\`;

    // 时间显示由 syncLyric 中的 updateTimeUI 处理，避免重复更新
  }

  function clearLyric() {
    if (lyricTimer) clearInterval(lyricTimer);
    lyricBox.innerHTML = '';
    lyricArr = [];
  }

  // 修复后的歌词解析函数 - 来自 index.html
  function parseLRC(lrc) {
    if (!lrc || typeof lrc !== 'string' || lrc.trim().length === 0) {
      return [];
    }

    const lines = lrc.split(/\\r?\\n/);
    const res = [];

    // 修正正则表达式：应该匹配 [mm:ss.xxx] 格式
    const timeRegex = /\\[(\\d{1,2}):(\\d{2})(?:\\.(\\d{1,3}))?\\]/g;

    for (const line of lines) {
      if (!line.trim()) continue;

      const timeMatches = [];
      let match;

      // 重置正则表达式的 lastIndex
      timeRegex.lastIndex = 0;

      // 找到所有时间戳
      while ((match = timeRegex.exec(line)) !== null) {
        timeMatches.push({
          min: parseInt(match[1]),
          sec: parseInt(match[2]),
          ms: match[3] ? parseInt(match[3].padEnd(3, '0')) : 0
        });
      }

      // 提取歌词文本（移除所有时间标签）
      const lrcText = line.replace(/\\[\\d{1,2}:\\d{2}(?:\\.\\d{1,3})?\\]/g, '').trim();

      // 如果有时间戳和歌词文本，添加到结果中
      if (timeMatches.length > 0 && lrcText) {
        timeMatches.forEach(timeData => {
          const time = timeData.min * 60 + timeData.sec + timeData.ms / 1000;
          res.push({ time, lrc: lrcText });
        });
      }
    }

    // 按时间排序
    res.sort((a, b) => a.time - b.time);

    return res;
  }

  // 歌词渲染 - 来自 index.html
  function renderLyricPanel() {
    if(!lyricArr || lyricArr.length === 0) {
      lyricBox.innerHTML = "<span style='color:#888;'>（无歌词）</span>";
      return;
    }
    lyricBox.innerHTML = lyricArr.map(item=>\`<div>\${item.lrc}</div>\`).join('');
  }

  // 歌词滚动高亮 - 修复版本，来自 index.html
  function syncLyric() {
    if(!sound || !lyricArr.length) return;
    const rawTime = sound.seek();
    const t = rawTime; // 暂时不使用偏移，可以后续添加
    updateTimeUI(rawTime, sound.duration() || 0); // 显示真实播放时间

    // 找到当前应该高亮的歌词行
    let currentIndex = -1;
    for(let i = 0; i < lyricArr.length; i++) {
      if(t >= lyricArr[i].time) {
        currentIndex = i;
      } else {
        break;
      }
    }

    // 高亮当前行
    const lines = lyricBox.querySelectorAll("div");
    lines.forEach((line, idx)=>{
      if(idx === currentIndex) {
        line.classList.add('current');
        // 只有在用户没有手动滚动歌词面板时才自动滚动歌词
        if(!isUserScrolling) {
          // 使用更精确的滚动方式，只在歌词面板内滚动
          const panelRect = lyricBox.getBoundingClientRect();
          const lineRect = line.getBoundingClientRect();
          const panelCenter = panelRect.top + panelRect.height / 2;
          const lineCenter = lineRect.top + lineRect.height / 2;
          const offset = lineCenter - panelCenter;

          // 如果歌词行不在面板中心附近，则滚动
          if (Math.abs(offset) > 20) {
            lyricBox.scrollBy({
              top: offset,
              behavior: 'smooth'
            });
          }
        }
      } else {
        line.classList.remove('current');
      }
    });
    updateProgress();
  }

  // 时间显示更新函数
  function updateTimeUI(cur, dur) {
    function fmt(t) {
      let m = Math.floor(t/60);
      let s = Math.floor(t%60);
      return (m<10?'0':'')+m+':'+(s<10?'0':'')+s;
    }
    timeDisplay.textContent = fmt(cur) + ' / ' + fmt(dur||0);
  }

  // 平滑滚动函数
  function smoothScroll(element, targetScrollTop, duration) {
    const startScrollTop = element.scrollTop;
    const distance = targetScrollTop - startScrollTop;
    const startTime = performance.now();

    function animation(currentTime) {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);

      // 使用缓动函数
      const easeInOutQuad = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      element.scrollTop = startScrollTop + distance * easeInOutQuad;

      if (progress < 1) {
        requestAnimationFrame(animation);
      }
    }

    requestAnimationFrame(animation);
  }

  function playSong(index){
    if(index<0 || index>=currentPlaylist.length) return;
    currentSongIndex = index;
    const song = currentPlaylist[index];
    const qualities = ['jymaster', 'jyeffect', 'flac', '320k'];
    let qualityIndex = 0;

    function tryNextQuality() {
      if (qualityIndex >= qualities.length) {
        showMessage('所有音质尝试失败，无法获取歌曲播放地址', 'error');
        clearLyric();
        return;
      }
      const quality = qualities[qualityIndex];
      fetch(\`/api/get-song-url?songId=\${song.id}&quality=\${quality}\`)
        .then(res => {
          if (!res.ok) throw new Error('获取播放地址失败');
          return res.json();
        })
        .then(data => {
          if (data.code === 200) {
            if (sound) sound.unload();
            if (progressInterval) clearInterval(progressInterval);
            clearLyric();

            sound = new Howl({
              src: [data.data],
              html5: true,
              onplay: () => {
                isPlaying = true;
                playPauseBtn.textContent = '暂停';
                updateNowPlaying();
                updateProgress();
                progressInterval = setInterval(updateProgress, 1000);
                if (lyricArr.length > 0) {
                  if (lyricTimer) clearInterval(lyricTimer);
                  lyricTimer = setInterval(syncLyric, 100); // 提高同步频率到100ms，来自 index.html
                }
              },
              onpause: () => {
                isPlaying = false;
                playPauseBtn.textContent = '播放';
                if (lyricTimer) clearInterval(lyricTimer);
              },
              onend: () => {
                clearInterval(progressInterval);
                if (lyricTimer) clearInterval(lyricTimer);
                clearLyric();
                if(currentSongIndex < currentPlaylist.length -1){
                  playSong(currentSongIndex +1);
                }else{
                  titleElem.textContent = '';
                  artistElem.textContent = '';
                  playPauseBtn.textContent = '播放';
                  isPlaying = false;
                  progressBar.style.width = '0%';
                  timeDisplay.textContent = '00:00 / 00:00';
                }
              },
              onloaderror: (id, error) => {
                qualityIndex++;
                tryNextQuality();
              },
              onplayerror: (id, error) => {
                qualityIndex++;
                tryNextQuality();
              }
            });

            // 歌词加载 - 使用 index.html 的逻辑
            lyricBox.innerHTML = '歌词加载中...';
            fetch(\`/api/lyric?songId=\${song.id}\`)
              .then(res => res.json())
              .then(data => {
                // 获取歌词数据
                const lyricData = data.lrc?.lyric || data.tlyric?.lyric || '';

                // 歌词解析
                lyricArr = parseLRC(lyricData);

                renderLyricPanel();

                if (lyricArr.length > 0) {
                  if (lyricTimer) clearInterval(lyricTimer);
                  lyricTimer = setInterval(syncLyric, 100); // 提高同步频率到100ms
                } else {
                  lyricBox.innerHTML = "<span style='color:#888;'>（无歌词）</span>";
                }
              }).catch(()=>{
                lyricBox.innerHTML = "<span style='color:#888;'>（歌词加载失败）</span>";
              });

            sound.play();
            updatePlayingHighlight();
          } else {
            qualityIndex++;
            tryNextQuality();
          }
        })
        .catch(err => {
          qualityIndex++;
          tryNextQuality();
        });
    }

    tryNextQuality();
  }

  function updateNowPlaying(){
    if(currentSongIndex<0 || currentSongIndex>=currentPlaylist.length){
      titleElem.textContent = '';
      artistElem.textContent = '';
      customPlayer.style.display = 'none';
      return;
    }
    const song = currentPlaylist[currentSongIndex];
    titleElem.textContent = song.name;
    artistElem.textContent = song.artists.map(a=>a.name).join(', ');
    customPlayer.style.display = 'block';
    updatePlayingHighlight();
  }

  function updatePlayingHighlight() {
    // 清除所有高亮
    document.querySelectorAll('#resultList li, #userPlaylistSongs li').forEach(el => {
      el.classList.remove('playing');
    });

    if (currentSongIndex < 0 || currentSongIndex >= currentPlaylist.length) return;

    // 高亮当前播放的歌曲
    if (currentPlaylist === searchResults) {
      const searchItems = document.querySelectorAll('#resultList li');
      if (searchItems[currentSongIndex]) {
        searchItems[currentSongIndex].classList.add('playing');
      }
    } else if (currentPlaylist === userPlaylist) {
      const playlistItems = document.querySelectorAll('#userPlaylistSongs li');
      if (playlistItems[currentSongIndex]) {
        playlistItems[currentSongIndex].classList.add('playing');
      }
    }
  }

  searchBtn.onclick = () => {
    const kw = searchInput.value.trim();
    if(!kw){
      showMessage('请输入关键词', 'warning');
      return;
    }
    currentKeyword = kw;
    currentPage = 1;
    fetchSongs(kw,1);
  }

  searchInput.addEventListener('keydown', (e) => {
    if(e.key === 'Enter'){
      searchBtn.click();
    }
  });

  prevBtn.onclick = () => {
    if(currentPage>1){
      fetchSongs(currentKeyword,currentPage-1);
    }
  }
  nextBtn.onclick = () => {
    if(currentPage*pageSize<totalCount){
      fetchSongs(currentKeyword,currentPage+1);
    }
  }

  playPauseBtn.onclick = () => {
    if (!sound) return;
    if (sound.playing()) {
      sound.pause();
      clearInterval(progressInterval);
      if (lyricTimer) clearInterval(lyricTimer);
    } else {
      sound.play();
      progressInterval = setInterval(updateProgress, 1000);
      if (lyricArr.length > 0 && !lyricTimer)
        lyricTimer = setInterval(syncLyric, 100); // 使用100ms频率，来自 index.html
    }
  }

  prevSongBtn.onclick = () => {
    if(currentSongIndex > 0){
      playSong(currentSongIndex -1);
    } else {
      showMessage('已经是第一首歌了', 'info');
    }
  }

  nextSongBtn.onclick = () => {
    if(currentSongIndex < currentPlaylist.length -1){
      playSong(currentSongIndex +1);
    } else {
      showMessage('已经是最后一首歌了', 'info');
    }
  }

  progressContainer.addEventListener('click', (e) => {
    if (!sound || !sound.duration()) return;
    const rect = progressContainer.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const duration = sound.duration();
    sound.seek(duration * clickPosition);
    updateProgress();
    syncLyric(); // 立即同步歌词
  });

  window.addEventListener('scroll', function() {
    isPageScrolling = true;
    clearTimeout(pageScrollTimeout);
    pageScrollTimeout = setTimeout(() => {
      isPageScrolling = false;
    }, 15000);
  });
  
  // 阻止歌词容器滚动时触发页面滚动 - 保留原有逻辑
  lyricBox.addEventListener('wheel', (e) => {
    const container = lyricBox;
    const { deltaY } = e;
    // 计算容器剩余可滚动的距离
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtTop = deltaY < 0 && scrollTop === 0;
    const isAtBottom = deltaY > 0 && scrollTop + clientHeight >= scrollHeight;

    // 当滚动到容器顶部或底部时，允许页面滚动
    if (isAtTop || isAtBottom) {
      return;
    }

    // 阻止默认的页面滚动行为
    e.preventDefault();
    // 手动滚动歌词容器
    container.scrollTop += deltaY;
  }, { passive: false });

  // 导入歌单功能
  async function importPlaylist() {
    const playlistIdInput = document.getElementById('playlist-id-input');
    const importBtn = document.getElementById('import-playlist-btn');
    const statusDiv = document.getElementById('import-status');

    const playlistId = playlistIdInput.value.trim();
    if (!playlistId) {
      showMessage('请输入歌单ID', 'warning');
      return;
    }

    // 禁用按钮，显示加载状态
    importBtn.disabled = true;
    importBtn.textContent = '导入中...';
    statusDiv.textContent = '正在获取歌单信息...';
    statusDiv.style.color = '#1db954';

    try {
      // 调用后端API获取歌单信息
      const response = await fetch(\`/api/playlist?id=\${encodeURIComponent(playlistId)}\`);
      if (!response.ok) {
        throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
      }

      const playlistData = await response.json();
      if (playlistData.code !== 200) {
        throw new Error(\`获取歌单失败: code=\${playlistData.code}\`);
      }

      statusDiv.textContent = \`获取到歌单: \${playlistData.name}，包含 \${playlistData.songCount} 首歌曲，正在导入...\`;

      let addedCount = 0;
      let skippedCount = 0;
      const totalSongs = playlistData.songs.length;

      // 批量添加歌曲到本地歌单
      for (let i = 0; i < playlistData.songs.length; i++) {
        const song = playlistData.songs[i];

        // 更新进度
        if (i % 10 === 0 || i === totalSongs - 1) {
          statusDiv.textContent = \`正在导入歌曲 \${i + 1}/\${totalSongs}...\`;
        }

        // 检查歌曲是否已存在
        const existingSong = userPlaylist.find(ps => ps.id === song.id);
        if (existingSong) {
          skippedCount++;
          continue;
        }

        // 添加歌曲到歌单，使用从后端获取的完整信息
        const playlistSong = {
          name: song.name || \`歌曲 \${song.id}\`, // 使用真实歌曲名称
          id: song.id,
          artists: song.artists || [{ name: '未知艺术家' }], // 使用真实艺术家信息
          album: song.album || { name: '未知专辑' }, // 使用真实专辑信息
          addedAt: new Date().toISOString(),
          fromPlaylist: playlistData.name
        };

        userPlaylist.push(playlistSong);
        addedCount++;
      }

      // 保存到localStorage
      saveUserPlaylist();
      renderUserPlaylist();

      // 显示结果
      const message = \`导入完成！添加了 \${addedCount} 首歌曲\${skippedCount > 0 ? \`，跳过 \${skippedCount} 首重复歌曲\` : ''}\`;
      showMessage(message, 'success');
      statusDiv.textContent = message;
      statusDiv.style.color = '#1db954';

      // 清空输入框
      playlistIdInput.value = '';

    } catch (error) {
      console.error('导入歌单失败:', error);
      const errorMessage = \`导入失败: \${error.message}\`;
      showMessage(errorMessage, 'error');
      statusDiv.textContent = errorMessage;
      statusDiv.style.color = '#ff4444';
    } finally {
      // 恢复按钮状态
      importBtn.disabled = false;
      importBtn.textContent = '导入歌单';
    }
  }

  // 绑定导入歌单按钮事件
  document.getElementById('import-playlist-btn').addEventListener('click', importPlaylist);

  // 支持回车键导入歌单
  document.getElementById('playlist-id-input').addEventListener('keydown', (e) => {
    if (e.key === 'Enter') {
      importPlaylist();
    }
  });

  renderUserPlaylist();
  })();

  
</script>
</body>
</html>`;
