addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

const SONG_URL_API = 'https://88.lxmusic.xn--fiqs8s/lxmusicv3/url/wy';

function withCors(response) {
  const newHeaders = new Headers(response.headers)
  newHeaders.set('Access-Control-Allow-Origin', '*')
  newHeaders.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  newHeaders.set('Access-Control-Allow-Headers', 'Content-Type')
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: newHeaders
  })
}

async function handleRequest(request) {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    })
  }

  const url = new URL(request.url)

  if (url.pathname === '/api/search') {
    const keyword = url.searchParams.get('keywords') || url.searchParams.get('s') || ''
    const limit = url.searchParams.get('limit') || '10'
    const offset = url.searchParams.get('offset') || '0'

    if (!keyword) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing keywords' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }))
    }

    const targetUrl = `https://netease-cloud-music-api.vercel.app/search?keywords=${encodeURIComponent(keyword)}&limit=${limit}&offset=${offset}`

    try {
      const resp = await fetch(targetUrl)
      const contentType = resp.headers.get('content-type') || 'application/json'
      const body = await resp.text()
      return withCors(new Response(body, {
        status: resp.status,
        headers: { 'content-type': contentType },
      }))
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }))
    }
  }

  if (url.pathname === '/api/get-song-url') {
    const songId = url.searchParams.get('songId');
    const quality = url.searchParams.get('quality') || 'flac';
    if (!songId) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing songId' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
    const targetUrl = `${SONG_URL_API}/${songId}/${quality}`;
    try {
      const resp = await fetch(targetUrl);
      const data = await resp.json();
      if (data.data) {
        const songUrl = data.data.replace(/\\/g, '');
        return withCors(new Response(JSON.stringify({ code: 200, data: encodeURI(songUrl) }), {
          status: 200,
          headers: { 'content-type': 'application/json;charset=UTF-8' },
        }));
      } else {
        return withCors(new Response(JSON.stringify({ code: 404, msg: 'song url not found' }), {
          status: 404,
          headers: { 'content-type': 'application/json;charset=UTF-8' },
        }));
      }
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
  }

  if (url.pathname === '/api/lyric') {
    const songId = url.searchParams.get('songId');
    if (!songId) {
      return withCors(new Response(JSON.stringify({ code: 400, msg: 'missing songId' }), {
        status: 400,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
    const targetUrl = `https://netease-cloud-music-api.vercel.app/lyric?id=${songId}`;
    try {
      const resp = await fetch(targetUrl);
      const contentType = resp.headers.get('content-type') || 'application/json';
      const body = await resp.text();
      return withCors(new Response(body, {
        status: resp.status,
        headers: { 'content-type': contentType },
      }));
    } catch (err) {
      return withCors(new Response(JSON.stringify({ code: 500, msg: err.message }), {
        status: 500,
        headers: { 'content-type': 'application/json;charset=UTF-8' },
      }));
    }
  }

  if (url.pathname === '/') {
    return withCors(new Response(htmlContent, {
      headers: { 'content-type': 'text/html;charset=UTF-8' },
    }))
  }

  return withCors(new Response('Not Found', { status: 404 }))
}

const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🎵 Music Station</title>
<script src="https://cdnjs.cloudflare.com/ajax/libs/howler/2.2.4/howler.min.js"></script>
<style>
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ddd;
    padding: 20px;
    margin: 0;
    min-height: 100vh;
  }
  .container {
    max-width: 1000px;
    margin: auto;
    background: rgba(0,0,0,0.6);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 0 15px rgba(0,0,0,0.7);
  }
  h1, h2 {
    text-align: center;
    color: #a3cef1;
    margin-bottom: 20px;
    user-select: none;
  }
  input[type=text] {
    width: 70%;
    padding: 10px;
    font-size: 16px;
    border-radius: 25px;
    border: none;
    outline: none;
  }
  button {
    padding: 10px 18px;
    font-size: 16px;
    border: none;
    border-radius: 25px;
    margin-left: 10px;
    cursor: pointer;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    user-select: none;
    transition: background 0.3s;
  }
  button:hover {
    background: linear-gradient(45deg, #89aefa, #8a6dfa);
  }
  #loading {
    text-align:center;
    margin: 10px 0;
    font-weight: 600;
    color: #a3cef1;
  }
  ul {
    list-style: none;
    padding-left: 0;
    max-height: 300px;
    overflow-y: auto;
    margin: 0;
  }
  li {
    padding: 10px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-radius: 8px;
    transition: background 0.3s;
  }
  li:hover {
    background: rgba(255,255,255,0.1);
  }
  li.playing {
    background: linear-gradient(45deg, #89aefa, #8a6dfa);
    color: white;
    font-weight: bold;
  }
  .song-info {
    flex: 1;
    user-select: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .song-actions {
    flex-shrink: 0;
    margin-left: 15px;
    display: flex;
    gap: 10px;
  }
  .song-actions button {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 20px;
    background: rgba(255,255,255,0.15);
    color: white;
    border: none;
    cursor: pointer;
    user-select: none;
    transition: background 0.3s;
  }
  .song-actions button:hover {
    background: rgba(255,255,255,0.3);
  }
  #pagination {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    gap: 10px;
    user-select: none;
    color: #a3cef1;
  }
  #pagination button {
    background: none;
    border: 1px solid #a3cef1;
    border-radius: 20px;
    color: #a3cef1;
    padding: 6px 14px;
    cursor: pointer;
    transition: background 0.3s;
  }
  #pagination button:disabled {
    border-color: #555;
    color: #555;
    cursor: default;
  }
  #pagination button:not(:disabled):hover {
    background: #a3cef1;
    color: #222;
  }
  #nowPlaying {
    text-align: center;
    margin-top: 20px;
    font-weight: 600;
    user-select: none;
  }
  .progress-container {
    width: 100%;
    height: 8px;
    background-color: rgba(255,255,255,0.1);
    border-radius: 4px;
    margin: 10px 0;
    cursor: pointer;
    position: relative;
  }
  .progress-bar {
    width: 0%;
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.1s linear;
  }
  .time-display {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #aaa;
  }
  .control-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
  }
  .control-buttons button {
    padding: 10px 18px;
    font-size: 16px;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    user-select: none;
    transition: background 0.3s;
  }
  .control-buttons button:hover {
    background: linear-gradient(45deg, #89aefa, #8a6dfa);
  }
  .player-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
  }
  #lyricBox {
    margin-top:25px;
    text-align:center;
    color:#ccc;
    min-height:80px;
    font-size:18px;
    line-height:2;
    word-break:break-all;
    max-height:220px;
    overflow-y: auto; /* 确保歌词在自身容器内滚动 */
    padding:5px 0;
    transition: all .2s;
  }
  #lyricBox .current {
    color:#00d4ff;
    background:rgba(0,212,255,0.08);
    font-weight:bold;
  }
</style>
</head>
<body>
  <div class="container">
    <h1>🎵 Music Station</h1>
    <div style="text-align:center;">
      <input id="searchInput" type="text" placeholder="输入歌曲名或歌手名" />
      <button id="searchBtn">搜索</button>
    </div>
    <div id="loading" style="display:none;">搜索中...</div>
    <ul id="resultList"></ul>
    <div id="pagination">
      <button id="prevBtn" disabled>上一页</button>
      <span id="pageInfo">第 1 页</span>
      <button id="nextBtn" disabled>下一页</button>
    </div>

    <h2>我的歌单</h2>
    <ul id="userPlaylistSongs"></ul>

    <div class="player-section">
      <div id="nowPlaying">当前未播放</div>
      <div class="time-display" id="timeDisplay">00:00 / 00:00</div>
      <div class="progress-container" id="progressContainer">
        <div class="progress-bar" id="progressBar"></div>
      </div>
      <div class="control-buttons">
        <button id="prevSongBtn">上一首</button>
        <button id="playPauseBtn">播放</button>
        <button id="nextSongBtn">下一首</button>
      </div>
      <div id="lyricBox"></div>
    </div>
  </div>

<script>
const Howl = window.Howl;

(() => {
  const searchInput = document.getElementById('searchInput');
  const searchBtn = document.getElementById('searchBtn');
  const loading = document.getElementById('loading');
  const resultList = document.getElementById('resultList');
  const prevBtn = document.getElementById('prevBtn');
  const nextBtn = document.getElementById('nextBtn');
  const pageInfo = document.getElementById('pageInfo');
  const userPlaylistSongs = document.getElementById('userPlaylistSongs');
  const prevSongBtn = document.getElementById('prevSongBtn');
  const nextSongBtn = document.getElementById('nextSongBtn');
  const playPauseBtn = document.getElementById('playPauseBtn');
  const nowPlaying = document.getElementById('nowPlaying');
  const progressContainer = document.getElementById('progressContainer');
  const progressBar = document.getElementById('progressBar');
  const timeDisplay = document.getElementById('timeDisplay');
  const lyricBox = document.getElementById('lyricBox');

  let sound = null;
  let progressInterval = null;
  let lyricTimer = null;
  let lyricArr = [];

  let currentPage = 1;
  const pageSize = 10;
  let totalCount = 0;
  let currentKeyword = '';
  let searchResults = [];
  let userPlaylist = JSON.parse(localStorage.getItem('userPlaylist') || '[]');
  let currentPlaylist = [];
  let currentSongIndex = -1;
  let isPlaying = false;
  let isUserScrolling = false;
  let userScrollTimer = null;
  let isAutoScrolling = false;
  let isPageScrolling = false;
  let pageScrollTimeout = null;

  

  function saveUserPlaylist(){
    localStorage.setItem('userPlaylist', JSON.stringify(userPlaylist));
  }

  function renderUserPlaylist(){
    userPlaylistSongs.innerHTML = '';
    if(userPlaylist.length === 0){
      userPlaylistSongs.innerHTML = '<li style="padding:10px;color:#aaa;">歌单空空如也，快去添加喜欢的歌曲吧</li>';
      return;
    }
    userPlaylist.forEach((song,i)=>{
      const li = document.createElement('li');
      li.className = (currentPlaylist === userPlaylist && currentSongIndex === i) ? 'playing' : '';
      li.style.display = 'flex';
      li.style.justifyContent = 'space-between';
      li.style.alignItems = 'center';

      const info = document.createElement('div');
      const albumName = song.album?.name || song.al?.name || '未知专辑';
      info.textContent = \`\${song.name} - \${song.artists.map(a=>a.name).join(', ')} (\${albumName})\`;
      info.title = info.textContent;
      info.style.flex = '1';
      info.style.cursor = 'pointer';
      info.onclick = () => {
        currentPlaylist = userPlaylist;
        playSong(i);
      }

      const actions = document.createElement('div');
      actions.style.display = 'flex';
      actions.style.gap = '10px';

      const playBtn = document.createElement('button');
      playBtn.textContent = '播放';
      playBtn.onclick = (e) => {
        e.stopPropagation();
        currentPlaylist = userPlaylist;
        playSong(i);
      };

      const delBtn = document.createElement('button');
      delBtn.textContent = '删除';
      delBtn.style.background = '#dc3545';
      delBtn.onclick = (e) => {
        e.stopPropagation();
        userPlaylist.splice(i,1);
        saveUserPlaylist();
        renderUserPlaylist();
        if(currentPlaylist === userPlaylist){
          if(currentSongIndex === i){
            if (sound) sound.pause();
            nowPlaying.textContent = '当前未播放';
            currentSongIndex = -1;
            clearLyric();
          } else if(currentSongIndex > i){
            currentSongIndex--;
          }
        }
      };

      actions.appendChild(playBtn);
      actions.appendChild(delBtn);

      li.appendChild(info);
      li.appendChild(actions);

      userPlaylistSongs.appendChild(li);
    });
  }

  function addToUserPlaylist(song){
    if(userPlaylist.find(s=>s.id === song.id)){
      alert('歌曲已在歌单！');
      return;
    }
    userPlaylist.push(song);
    saveUserPlaylist();
    renderUserPlaylist();
    alert('已添加到歌单');
  }

  function renderSearchResults(){
    resultList.innerHTML = '';
    if(searchResults.length === 0){
      resultList.innerHTML = '<li style="padding:10px;color:#aaa;">暂无搜索结果</li>';
      return;
    }
    searchResults.forEach((song,i)=>{
      const li = document.createElement('li');
      li.style.display = 'flex';
      li.style.justifyContent = 'space-between';
      li.style.alignItems = 'center';
      li.style.cursor = 'default';

      const info = document.createElement('div');
      const albumName = song.album?.name || song.al?.name || '未知专辑';
      info.textContent = \`\${song.name} - \${song.artists.map(a=>a.name).join(', ')} (\${albumName})\`;
      info.title = info.textContent;
      info.style.flex = '1';
      info.style.cursor = 'pointer';
      info.onclick = () => {
        currentPlaylist = searchResults;
        playSong(i);
      }

      const actions = document.createElement('div');
      actions.style.display = 'flex';
      actions.style.gap = '10px';

      const playBtn = document.createElement('button');
      playBtn.textContent = '播放';
      playBtn.onclick = (e) => {
        e.stopPropagation();
        currentPlaylist = searchResults;
        playSong(i);
      };let isUserScrolling = false;
      let pageScrollTimer = null;
      let userScrollTimer = null;

      const addBtn = document.createElement('button');
      addBtn.textContent = '加入歌单';
      addBtn.style.background = '#28a745';
      addBtn.onclick = (e) => {
        e.stopPropagation();
        addToUserPlaylist(song);
      };

      actions.appendChild(playBtn);
      actions.appendChild(addBtn);

      li.appendChild(info);
      li.appendChild(actions);

      resultList.appendChild(li);
    });
  }

  function updatePagination(){
    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage * pageSize >= totalCount;
    pageInfo.textContent = \`第 \${currentPage} 页 / 共 \${Math.ceil(totalCount/pageSize)} 页\`;
  }

  function fetchSongs(keyword,page=1){
    if(!keyword) return;
    loading.style.display = 'block';
    resultList.innerHTML = '';
    prevBtn.disabled = true;
    nextBtn.disabled = true;
    fetch(\`/api/search?s=\${encodeURIComponent(keyword)}&offset=\${(page-1)*pageSize}&limit=\${pageSize}\`).then(res=>{
      if(!res.ok) throw new Error('网络错误');
      return res.json();
    }).then(data=>{
      loading.style.display = 'none';
      if(!data.result || !data.result.songs || data.result.songs.length === 0){
        searchResults = [];
        totalCount = 0;
        currentSongIndex = -1;
        renderSearchResults();
        updatePagination();
        return;
      }
      searchResults = data.result.songs;
      totalCount = data.result.songCount || 0;
      currentPage = page;
      renderSearchResults();
      updatePagination();
    }).catch(err=>{
      loading.style.display = 'none';
      alert('搜索失败：' + err.message);
    });
  }

  function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return \`\${mins.toString().padStart(2, '0')}:\${secs.toString().padStart(2, '0')}\`;
  }

  function updateProgress() {
    if (!sound || !sound.playing()) return;

    const duration = sound.duration() || 0;
    const position = sound.seek() || 0;
    const progress = duration ? (position / duration) * 100 : 0;

    progressBar.style.width = \`\${progress}%\`;

    const currentTime = formatTime(position);
    const totalTime = formatTime(duration);
    timeDisplay.textContent = \`\${currentTime} / \${totalTime}\`;
  }

  function clearLyric() {
    if (lyricTimer) clearInterval(lyricTimer);
    lyricBox.innerHTML = '';
    lyricArr = [];
  }

  function parseLRC(lrc) {
    if (!lrc || typeof lrc !== 'string' || lrc.trim().length === 0) return [];
    const lines = lrc.split(/\\r?\\n/);
    const res = [];
    
    lines.forEach(line => {
      // 匹配时间标签 [mm:ss.xxx] 或 [mm:ss]
      const timeMatch = line.match(/\\[(\\d{1,2}):(\\d{2})\\.?(\\d{0,3})?\\]/);
      if (timeMatch) {
        const min = parseInt(timeMatch[1], 10);
        const sec = parseInt(timeMatch[2], 10);
        const ms = timeMatch[3] ? parseInt(timeMatch[3].padEnd(3, '0'), 10) : 0;
        const time = min * 60 + sec + ms / 1000;
        
        // 获取歌词文本，移除所有时间标签
        const lrcText = line.replace(/\\[\\d{1,2}:\\d{2}\\.?\\d{0,3}?\\]/g, '').trim();
        
        if (lrcText) {
          res.push({ time, lrc: lrcText });
        }
      }
    });
    
    // 按时间排序并去重
    res.sort((a, b) => a.time - b.time);
    
    // 去除重复的歌词（相同时间和文本）
    const uniqueLyrics = [];
    for (let i = 0; i < res.length; i++) {
      const current = res[i];
      const prev = uniqueLyrics[uniqueLyrics.length - 1];
      
      if (!prev || prev.lrc !== current.lrc || Math.abs(prev.time - current.time) > 0.1) {
        uniqueLyrics.push(current);
      }
    }
    
    return uniqueLyrics;
  }

  function renderLyricPanel() {
    if (!lyricArr || lyricArr.length === 0) {
      lyricBox.innerHTML = "<div style='color:#888; text-align:center; padding:20px;'>（无歌词）</div>";
      return;
    }
    
    const lyricHTML = lyricArr.map((item, index) => 
      \`<div class="lyric-line" data-index="\${index}" data-time="\${item.time}">\${item.lrc}</div>\`
    ).join('');
    
    lyricBox.innerHTML = lyricHTML;
  }

  // 优化歌词同步与滚动
  function syncLyric() {
    if (!sound || !lyricArr.length) return;
    if (isUserScrolling) return;
  
    const currentTime = sound.seek() || 0;
    let activeIndex = -1;
  
    for (let i = 0; i < lyricArr.length; i++) {
      if (currentTime >= lyricArr[i].time) {
        activeIndex = i;
      } else {
        break;
      }
    }
  
    const lyricLines = lyricBox.querySelectorAll('.lyric-line');
    lyricLines.forEach((line, index) => {
      line.classList.toggle('current', index === activeIndex);
      if (index === activeIndex && !isUserScrolling) {
        isAutoScrolling = true;
        // 手动计算滚动位置
        const rectLyricBox = lyricBox.getBoundingClientRect();
        const rectLine = line.getBoundingClientRect();
        const scrollTop = lyricBox.scrollTop + rectLine.top - rectLyricBox.top - (lyricBox.clientHeight - rectLine.height) / 2;
  
        // 使用平滑滚动动画
        smoothScroll(lyricBox, scrollTop, 400);
        setTimeout(() => { 
          isAutoScrolling = false; 
        }, 400);
      }
    });
  }
  
  

  function playSong(index){
    if(index<0 || index>=currentPlaylist.length) return;
    currentSongIndex = index;
    const song = currentPlaylist[index];
    const qualities = ['jymaster', 'jyeffect', 'flac', '320k'];
    let qualityIndex = 0;

    function tryNextQuality() {
      if (qualityIndex >= qualities.length) {
        alert('所有音质尝试失败……,无法获取歌曲播放地址');
        clearLyric();
        return;
      }
      const quality = qualities[qualityIndex];
      fetch(\`/api/get-song-url?songId=\${song.id}&quality=\${quality}\`)
        .then(res => {
          if (!res.ok) throw new Error('获取播放地址失败');
          return res.json();let isUserScrolling = false;
          let userScrollTimer = null;
          let isAutoScrolling = false;
          let isPageScrolling = false;
          let pageScrollTimeout = null;
        })
        .then(data => {
          if (data.code === 200) {
            if (sound) sound.unload();
            if (progressInterval) clearInterval(progressInterval);
            clearLyric();

            sound = new Howl({
              src: [data.data],
              html5: true,
              onplay: () => {
                isPlaying = true;
                playPauseBtn.textContent = '暂停';
                updateNowPlaying();
                updateProgress();
                progressInterval = setInterval(updateProgress, 1000);
                if (lyricArr.length > 0) {
                  if (lyricTimer) clearInterval(lyricTimer);
                  lyricTimer = setInterval(syncLyric, 300);
                }
              },
              onpause: () => {
                isPlaying = false;
                playPauseBtn.textContent = '播放';
                if (lyricTimer) clearInterval(lyricTimer);
              },
              onend: () => {
                clearInterval(progressInterval);
                if (lyricTimer) clearInterval(lyricTimer);
                clearLyric();
                if(currentSongIndex < currentPlaylist.length -1){
                  playSong(currentSongIndex +1);
                }else{
                  nowPlaying.textContent = '播放结束';
                  playPauseBtn.textContent = '播放';
                  isPlaying = false;
                  progressBar.style.width = '0%';
                  timeDisplay.textContent = '00:00 / 00:00';
                }
              },
              onloaderror: (id, error) => {
                qualityIndex++;
                tryNextQuality();
              },
              onplayerror: (id, error) => {
                qualityIndex++;
                tryNextQuality();
              }
            });

            // 歌词加载
            lyricBox.innerHTML = '歌词加载中...';
            fetch(\`/api/lyric?songId=\${song.id}\`)
              .then(res => res.json())
              .then(data => {
                let lrc = '';
                if(data.lrc && data.lrc.lyric) lrc = data.lrc.lyric;
                else if (data.tlyric && data.tlyric.lyric) lrc = data.tlyric.lyric;
                if (lrc && lrc.trim()) {
                  lyricArr = parseLRC(lrc);
                  renderLyricPanel();
                  if (lyricTimer) clearInterval(lyricTimer);
                  lyricTimer = setInterval(syncLyric, 300);
                } else {
                  lyricBox.innerHTML = "<span style='color:#888;'>（无歌词）</span>";
                }
              }).catch(()=>{
                lyricBox.innerHTML = "<span style='color:#888;'>（歌词加载失败）</span>";
              });

            sound.play();
            renderUserPlaylist();
            renderSearchResults();
          } else {
            qualityIndex++;
            tryNextQuality();
          }
        })
        .catch(err => {
          qualityIndex++;
          tryNextQuality();
        });
    }

    tryNextQuality();
  }

  function updateNowPlaying(){
    if(currentSongIndex<0 || currentSongIndex>=currentPlaylist.length){
      nowPlaying.textContent = '当前未播放';
      return;
    }
    const song = currentPlaylist[currentSongIndex];
    nowPlaying.textContent = \`正在播放：\${song.name} - \${song.artists.map(a=>a.name).join(',')}\`;
  }

  searchBtn.onclick = () => {
    const kw = searchInput.value.trim();
    if(!kw){
      alert('请输入关键词');
      return;
    }
    currentKeyword = kw;
    currentPage = 1;
    fetchSongs(kw,1);
  }

  searchInput.addEventListener('keydown', (e) => {
    if(e.key === 'Enter'){
      searchBtn.click();
    }
  });

  prevBtn.onclick = () => {
    if(currentPage>1){
      fetchSongs(currentKeyword,currentPage-1);
    }
  }
  nextBtn.onclick = () => {
    if(currentPage*pageSize<totalCount){
      fetchSongs(currentKeyword,currentPage+1);
    }
  }

  playPauseBtn.onclick = () => {
    if (!sound) return;
    if (sound.playing()) {
      sound.pause();
      clearInterval(progressInterval);
      if (lyricTimer) clearInterval(lyricTimer);
    } else {
      sound.play();
      progressInterval = setInterval(updateProgress, 1000);
      if (lyricArr.length > 0 && !lyricTimer)
        lyricTimer = setInterval(syncLyric, 300);
    }
  }

  prevSongBtn.onclick = () => {
    if(currentSongIndex > 0){
      playSong(currentSongIndex -1);
    } else {
      alert('已经是第一首歌了');
    }
  }

  nextSongBtn.onclick = () => {
    if(currentSongIndex < currentPlaylist.length -1){
      playSong(currentSongIndex +1);
    } else {
      alert('已经是最后一首歌了');
    }
  }

  progressContainer.addEventListener('click', (e) => {
    if (!sound || !sound.duration()) return;
    const rect = progressContainer.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const duration = sound.duration();
    sound.seek(duration * clickPosition);
    updateProgress();
    syncLyric();
  });

  window.addEventListener('scroll', function() {
    isPageScrolling = true;
    clearTimeout(pageScrollTimeout);
    pageScrollTimeout = setTimeout(() => {
      isPageScrolling = false;
    }, 15000);
  });
  
  lyricBox.addEventListener('scroll', () => {
    if (isAutoScrolling) return;
    isUserScrolling = true;
    if (userScrollTimer) clearTimeout(userScrollTimer);
    userScrollTimer = setTimeout(() => {
      isUserScrolling = false;
    }, 200);
  });


  // 阻止歌词容器滚动时触发页面滚动
  lyricBox.addEventListener('wheel', (e) => {
    const container = lyricBox;
    const { deltaY } = e;
    // 计算容器剩余可滚动的距离
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtTop = deltaY < 0 && scrollTop === 0;
    const isAtBottom = deltaY > 0 && scrollTop + clientHeight >= scrollHeight;

    // 当滚动到容器顶部或底部时，允许页面滚动
    if (isAtTop || isAtBottom) {
      return;
    }

    // 阻止默认的页面滚动行为
    e.preventDefault();
    // 手动滚动歌词容器
    container.scrollTop += deltaY;
  }, { passive: false });

  renderUserPlaylist();
  })();

  
</script>
</body>
</html>`;
