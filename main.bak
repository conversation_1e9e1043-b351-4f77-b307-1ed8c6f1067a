package main

import (
    "encoding/json"
    "fmt"
    "io/ioutil"
    "log"
    "net/http"
    "net/url"
    "strconv"
    "time"
)

type Song struct {
    Name    string `json:"name"`
    ID      int64  `json:"id"`
    Artists []struct {
        Name string `json:"name"`
    } `json:"artists"`
}

type SearchResult struct {
    Result struct {
        Songs []Song `json:"songs"`
    } `json:"result"`
}

func searchSongs(keyword string, page int) ([]Song, error) {
    start := time.Now()

    baseURL := "https://music.163.com/api/search/get/web"
    params := url.Values{}
    params.Set("s", keyword)
    params.Set("type", "1")
    params.Set("limit", "10")
    params.Set("offset", fmt.Sprintf("%d", (page-1)*10))

    reqURL := baseURL + "?" + params.Encode()
    log.Printf("[DEBUG] 构造请求 URL: %s", reqURL)

    client := &http.Client{}
    req, err := http.NewRequest("GET", reqURL, nil)
    if err != nil {
        log.Printf("[ERROR] 创建请求失败: %v", err)
        return nil, err
    }

    // 设置请求头
    req.Header.Set("Referer", "https://music.163.com")
    req.Header.Set("User-Agent", "Mozilla/5.0")
    log.Printf("[DEBUG] 请求头: Referer=%s, User-Agent=%s", req.Header.Get("Referer"), req.Header.Get("User-Agent"))

    resp, err := client.Do(req)
    if err != nil {
        log.Printf("[ERROR] 请求执行失败: %v", err)
        return nil, err
    }
    defer resp.Body.Close()
    log.Printf("[DEBUG] 响应状态: %s", resp.Status)

    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        log.Printf("[ERROR] 读取响应体失败: %v", err)
        return nil, err
    }

    // 限制打印长度，防止日志爆炸
    maxLogLen := 1000
    bodyPreview := string(body)
    if len(bodyPreview) > maxLogLen {
        bodyPreview = bodyPreview[:maxLogLen] + "...(截断)"
    }
    log.Printf("[DEBUG] 响应体内容: %s", bodyPreview)

    var result SearchResult
    err = json.Unmarshal(body, &result)
    if err != nil {
        log.Printf("[ERROR] JSON解析失败: %v", err)
        return nil, err
    }

    elapsed := time.Since(start)
    log.Printf("[INFO] searchSongs 执行完成，耗时: %v，找到歌曲数: %d", elapsed, len(result.Result.Songs))

    return result.Result.Songs, nil
}

func searchHandler(w http.ResponseWriter, r *http.Request) {
    log.Printf("[INFO] 收到请求: %s %s from %s", r.Method, r.URL.String(), r.RemoteAddr)

    keyword := r.URL.Query().Get("q")
    pageStr := r.URL.Query().Get("page")
    if keyword == "" {
        log.Printf("[WARN] 缺少参数 q")
        http.Error(w, "缺少参数 q", http.StatusBadRequest)
        return
    }

    page := 1
    if pageStr != "" {
        if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
            page = p
        } else {
            log.Printf("[WARN] 参数 page 无效: %s", pageStr)
        }
    }
    log.Printf("[INFO] 搜索关键词: %s, 页码: %d", keyword, page)

    songs, err := searchSongs(keyword, page)
    if err != nil {
        log.Printf("[ERROR] 搜索失败: %v", err)
        http.Error(w, "搜索失败: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    if err := json.NewEncoder(w).Encode(songs); err != nil {
        log.Printf("[ERROR] 写入响应失败: %v", err)
    }
}

func main() {
    fs := http.FileServer(http.Dir("./static"))
    http.Handle("/", fs)
    http.HandleFunc("/search", searchHandler)

    log.Println("服务器启动： :8086")
    log.Fatal(http.ListenAndServe(":8086", nil))
}